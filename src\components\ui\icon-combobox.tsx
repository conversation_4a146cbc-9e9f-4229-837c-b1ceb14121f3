'use client';

import * as LucideIcons from 'lucide-react';
import { Check } from 'lucide-react';
import * as React from 'react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Input } from './input';
import { IconAndValue } from '@/@types/LabelAndValue';

const iconList = [
  { label: 'Home', value: LucideIcons.Home },
  { label: 'User', value: LucideIcons.User },
  { label: 'Settings', value: LucideIcons.Settings },
  { label: 'Search', value: LucideIcons.Search },
  { label: 'Mail', value: LucideIcons.Mail },
  { label: 'Phone', value: LucideIcons.Phone },
  { label: 'Calendar', value: LucideIcons.Calendar },
  { label: 'Clock', value: LucideIcons.Clock },
  { label: 'Heart', value: LucideIcons.Heart },
  { label: 'Star', value: LucideIcons.Star },
  { label: 'Bookmark', value: LucideIcons.Bookmark },
  { label: 'Download', value: LucideIcons.Download },
  { label: 'Upload', value: LucideIcons.Upload },
  { label: 'Edit', value: LucideIcons.Edit },
  { label: 'Trash', value: LucideIcons.Trash },
  { label: 'Plus', value: LucideIcons.Plus },
  { label: 'Minus', value: LucideIcons.Minus },
  { label: 'X', value: LucideIcons.X },
  { label: 'Check', value: LucideIcons.Check },
  { label: 'ChevronLeft', value: LucideIcons.ChevronLeft },
  { label: 'ChevronRight', value: LucideIcons.ChevronRight },
  { label: 'ChevronUp', value: LucideIcons.ChevronUp },
  { label: 'ChevronDown', value: LucideIcons.ChevronDown },
  { label: 'ArrowLeft', value: LucideIcons.ArrowLeft },
  { label: 'ArrowRight', value: LucideIcons.ArrowRight },
  { label: 'ArrowUp', value: LucideIcons.ArrowUp },
  { label: 'ArrowDown', value: LucideIcons.ArrowDown },
  { label: 'Eye', value: LucideIcons.Eye },
  { label: 'EyeOff', value: LucideIcons.EyeOff },
  { label: 'Lock', value: LucideIcons.Lock },
  { label: 'Unlock', value: LucideIcons.Unlock },
  { label: 'Image', value: LucideIcons.Image },
  { label: 'File', value: LucideIcons.File },
  { label: 'Folder', value: LucideIcons.Folder },
  { label: 'Copy', value: LucideIcons.Copy },
  { label: 'Share', value: LucideIcons.Share },
  { label: 'Link', value: LucideIcons.Link },
  { label: 'ExternalLink', value: LucideIcons.ExternalLink },
  { label: 'Bell', value: LucideIcons.Bell },
  { label: 'BellOff', value: LucideIcons.BellOff },
  { label: 'MessageCircle', value: LucideIcons.MessageCircle },
  { label: 'Send', value: LucideIcons.Send },
  { label: 'Globe', value: LucideIcons.Globe },
  { label: 'Wifi', value: LucideIcons.Wifi },
  { label: 'Battery', value: LucideIcons.Battery },
  { label: 'Volume2', value: LucideIcons.Volume2 },
  { label: 'Play', value: LucideIcons.Play },
  { label: 'Pause', value: LucideIcons.Pause },
  { label: 'Square', value: LucideIcons.Square },
  { label: 'Circle', value: LucideIcons.Circle },
  { label: 'Triangle', value: LucideIcons.Triangle },
];

interface IconComboboxProps {
  value?: IconAndValue;
  onValueChange?: (value: IconAndValue) => void;
  placeholder?: string;
  className?: string;
  errorMessage?: string;
  disabled?: boolean;
}

export function IconCombobox({
  value,
  onValueChange,
  placeholder = 'Selecione o ícone do card',
  errorMessage,
  disabled,
}: IconComboboxProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
      modal
    >
      <PopoverTrigger
        className="w-full"
        disabled={disabled}
      >
        <Input
          placeholder={placeholder}
          className="w-full"
          label="Ícone"
          value={value?.label}
          errorMessage={errorMessage}
          readOnly
        />
      </PopoverTrigger>
      <PopoverContent
        className="max-w-[375px]"
        onWheel={(e) => {
          e.stopPropagation();
        }}
      >
        <Command>
          <CommandInput placeholder="Pesquisar ícone" />
          <CommandList>
            <CommandEmpty>Nenhum ícone encontrado.</CommandEmpty>
            <CommandGroup>
              <div className="grid grid-cols-5 gap-2">
                {iconList.map((icon) => (
                  <CommandItem
                    key={icon.label}
                    value={icon.label}
                    onSelect={() => {
                      onValueChange?.(icon as IconAndValue);
                      setOpen(false);
                    }}
                    className="flex flex-col items-center justify-center p-1 h-16 cursor-pointer hover:bg-accent rounded-md"
                  >
                    <icon.value
                      className={cn(
                        'h-6 w-6',
                        value?.label === icon.label
                          ? 'text-primary'
                          : 'text-muted-foreground',
                      )}
                    />
                    <span className="text-xs mt-1 text-center truncate w-full">
                      {icon.label}
                    </span>
                    {value?.label === icon.label && (
                      <Check className="absolute top-1 right-1 h-3 w-3 text-primary" />
                    )}
                  </CommandItem>
                ))}
              </div>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
